package com.kespra.v18.services;

import android.app.usage.UsageStats;
import android.app.usage.UsageStatsManager;
import android.content.Context;

import java.util.List;

public class UsageStatsService {

    public static String checkForegroundApp(Context context)
    {
        UsageStatsManager usageStatsManager = (UsageStatsManager) context.getSystemService(Context.USAGE_STATS_SERVICE);

        // Get app usage stats for the last 10 seconds
        long endTime = System.currentTimeMillis();
        long startTime = endTime - 10000; // 10 seconds ago

        List<UsageStats> usageStatsList = usageStatsManager.queryUsageStats(
                UsageStatsManager.INTERVAL_DAILY, startTime, endTime);

        if (usageStatsList == null || usageStatsList.isEmpty()) {
            return "Unknown"; // No data available
        }

        // Find the app with the most recent lastTimeUsed (without sorting)
        UsageStats mostRecentApp = null;
        for (UsageStats usageStats : usageStatsList) {
            if (mostRecentApp == null || usageStats.getLastTimeUsed() > mostRecentApp.getLastTimeUsed()) {
                mostRecentApp = usageStats;
            }
        }

        // Return the package name of the foreground app
        return (mostRecentApp != null) ? mostRecentApp.getPackageName() : "Unknown";

    }

}

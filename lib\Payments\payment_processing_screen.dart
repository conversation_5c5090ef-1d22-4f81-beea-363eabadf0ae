import 'package:flutter/material.dart';
import 'package:v18ui/payments/payment_models.dart';
import 'package:v18ui/payments/upi_payment_service.dart';
import 'package:v18ui/DataObjects/subscription.dart';

class PaymentProcessingScreen extends StatefulWidget {
  final SubscriptionPlan plan;
  final String orderId;
  final double? finalAmount;
  final Coupon? appliedCoupon;

  const PaymentProcessingScreen({
    super.key,
    required this.plan,
    required this.orderId,
    this.finalAmount,
    this.appliedCoupon,
  });

  @override
  State<PaymentProcessingScreen> createState() =>
      _PaymentProcessingScreenState();
}

class _PaymentProcessingScreenState extends State<PaymentProcessingScreen> {
  PaymentStatus _currentStatus = PaymentStatus.pending;
  String _statusMessage = 'Initializing payment...';
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _initiatePayment();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // Prevent back navigation during payment processing
        if (_isProcessing) {
          return false;
        }
        return true;
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Payment Processing'),
          backgroundColor: Colors.blue.shade100,
          automaticallyImplyLeading: !_isProcessing,
        ),
        body: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildStatusIcon(),
              const SizedBox(height: 32),
              _buildPlanDetails(),
              const SizedBox(height: 32),
              _buildStatusMessage(),
              const SizedBox(height: 32),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusIcon() {
    IconData iconData;
    Color iconColor;

    switch (_currentStatus) {
      case PaymentStatus.pending:
        iconData = Icons.payment;
        iconColor = Colors.orange;
        break;
      case PaymentStatus.success:
        iconData = Icons.check_circle;
        iconColor = Colors.green;
        break;
      case PaymentStatus.failed:
        iconData = Icons.error;
        iconColor = Colors.red;
        break;
      case PaymentStatus.cancelled:
        iconData = Icons.cancel;
        iconColor = Colors.grey;
        break;
    }

    return Container(
      width: 100,
      height: 100,
      decoration: BoxDecoration(
        color: iconColor.withOpacity(0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(
        iconData,
        size: 50,
        color: iconColor,
      ),
    );
  }

  Widget _buildPlanDetails() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Plan:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(widget.plan.name),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Duration:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(widget.plan.durationText),
            ],
          ),
          const SizedBox(height: 8),
          // Show coupon discount if applied
          if (widget.appliedCoupon != null) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Original Amount:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  widget.plan.formattedPrice,
                  style: const TextStyle(
                    decoration: TextDecoration.lineThrough,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Discount (${widget.appliedCoupon!.code}):',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  '-₹${widget.appliedCoupon!.calculateDiscount(widget.plan.price).toStringAsFixed(0)}',
                  style: const TextStyle(color: Colors.green),
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Divider(),
            const SizedBox(height: 8),
          ],
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                widget.appliedCoupon != null ? 'Final Amount:' : 'Amount:',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(
                '₹${(widget.finalAmount ?? widget.plan.price).toStringAsFixed(0)}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Order ID:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(
                widget.orderId,
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusMessage() {
    return Column(
      children: [
        if (_isProcessing) ...[
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
        ],
        Text(
          _statusMessage,
          textAlign: TextAlign.center,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    switch (_currentStatus) {
      case PaymentStatus.pending:
        return Column(
          children: [
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isProcessing ? null : _retryPayment,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text(
                  'Retry Payment',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isProcessing ? null : _verifyPayment,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text(
                  'I have completed payment',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ),
            const SizedBox(height: 12),
            TextButton(
              onPressed: _isProcessing ? null : _cancelPayment,
              child: const Text('Cancel'),
            ),
          ],
        );
      case PaymentStatus.success:
        return SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _goToHome,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text(
              'Continue',
              style: TextStyle(color: Colors.white),
            ),
          ),
        );
      case PaymentStatus.failed:
      case PaymentStatus.cancelled:
        return Column(
          children: [
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _retryPayment,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text(
                  'Try Again',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ),
            const SizedBox(height: 12),
            TextButton(
              onPressed: _goBack,
              child: const Text('Go Back'),
            ),
          ],
        );
    }
  }

  void _initiatePayment() async {
    setState(() {
      _isProcessing = true;
      _statusMessage = 'Initiating UPI payment...';
    });

    try {
      final paymentAmount = widget.finalAmount ?? widget.plan.price;
      final result = await UpiPaymentService.initiatePayment(
        amount: paymentAmount,
        orderId: widget.orderId,
      );

      setState(() {
        _currentStatus = result.status;
        _statusMessage = result.message;
        _isProcessing = false;
      });

      if (result.isPending) {
        // Wait a bit and then show verification option
        await Future.delayed(const Duration(seconds: 3));
        setState(() {
          _statusMessage =
              'Please complete the payment in your UPI app and then click "I have completed payment" button.';
        });
      }
    } catch (e) {
      setState(() {
        _currentStatus = PaymentStatus.failed;
        _statusMessage = 'Payment failed: ${e.toString()}';
        _isProcessing = false;
      });
    }
  }

  void _retryPayment() {
    _initiatePayment();
  }

  void _verifyPayment() async {
    setState(() {
      _isProcessing = true;
      _statusMessage = 'Verifying payment...';
    });

    try {
      final result = await UpiPaymentService.verifyPayment(widget.orderId);

      setState(() {
        _currentStatus = result.status;
        _statusMessage = result.message;
        _isProcessing = false;
      });

      if (result.isSuccess) {
        // Only update subscription if payment is VERIFIED as successful
        await _updateSubscriptionStatus();

        // Show success message
        setState(() {
          _statusMessage =
              'Payment successful! Your subscription is now active.';
        });
      } else {
        // Show appropriate error message based on result
        setState(() {
          if (_statusMessage.contains('not implemented')) {
            _statusMessage +=
                '\n\nFor testing purposes, please contact the developer to manually activate your subscription.';
          } else {
            _statusMessage +=
                '\n\nPlease try again or use a different payment method.';
          }
        });
      }
    } catch (e) {
      setState(() {
        _currentStatus = PaymentStatus.failed;
        _statusMessage =
            'Verification failed: ${e.toString()}\n\nPlease try again later.';
        _isProcessing = false;
      });
    }
  }

  void _cancelPayment() {
    setState(() {
      _currentStatus = PaymentStatus.cancelled;
      _statusMessage = 'Payment cancelled by user';
    });
  }

  void _goToHome() {
    Navigator.pop(
        context,
        PaymentResult(
          status: PaymentStatus.success,
          transactionId: widget.orderId,
          message: 'Payment successful',
        ));
  }

  void _goBack() {
    Navigator.pop(context);
  }

  Future<void> _updateSubscriptionStatus() async {
    try {
      // Calculate expiry date based on plan duration
      final expiryDate =
          DateTime.now().add(Duration(days: widget.plan.durationInDays));

      // Update subscription status
      await Subscription.activateSubscription(expiryDate.toIso8601String());

      setState(() {
        _statusMessage =
            'Subscription activated successfully! You now have access to all premium features.';
      });
    } catch (e) {
      print('❌ Error updating subscription: $e');
      setState(() {
        _statusMessage =
            'Payment successful, but there was an issue activating your subscription. Please contact support.';
      });
    }
  }
}

import 'package:v18ui/DataObjects/blocked_apps.dart';
import 'package:v18ui/DataObjects/config_manager.dart';
import 'package:v18ui/Services/Service.dart';
import 'package:v18ui/Services/foreground_service.dart';

class BlockAppsService {
  List<BlockedApp>? appsBlocked;
  bool? serviceActive;
  final bool userGenerated = false;

  BlockAppsService({this.appsBlocked, this.serviceActive});

  BlockAppsService? get blockAppsServiceData =>
      ConfigManager().services?.appBlockService;

  List<BlockedApp>? get appsBlockedData => blockAppsServiceData?.appsBlocked;

  set blockAppsServiceData(BlockAppsService? value) {
    ConfigManager().services!.appBlockService = value;
  }

  set appsBlockedData(List<BlockedApp>? value) {
    blockAppsServiceData!.appsBlocked = value;
  }

  factory BlockAppsService.fromJson(Map<String, dynamic> json) {
    return BlockAppsService(
      serviceActive: json['serviceActive'],
      appsBlocked: List<BlockedApp>.from(json['appsBlocked']
          ?.map((app) => BlockedApp.fromJson(app as Map<String, dynamic>))
          .toList()),
    );
  }

  Map<String, dynamic> toJson() => {
        'serviceActive': serviceActive,
        'appsBlocked': appsBlocked?.map((x) => x.toJson()).toList() ?? [],
      };

  Future<bool> updateService() async {
    bool result =
        await Service().updateService('appsBlocked', toJson()['appsBlocked']);

    // Start or restart foreground service when service is updated
    if (result && serviceActive == true) {
      await _startForegroundServiceWithBlockedApps();
    }

    return result;
  }

  Future<bool> deleteService() async {
    bool result = await Service().deleteService('appsBlocked');

    // Stop foreground service when service is deleted
    if (result) {
      await ForegroundService.stopForegroundService();
    }

    return result;
  }

  /// Activates the app blocking service and starts the foreground service
  Future<bool> activateService() async {
    serviceActive = true;
    bool result = await updateService();

    if (result) {
      await _startForegroundServiceWithBlockedApps();
    }

    return result;
  }

  /// Deactivates the app blocking service and stops the foreground service
  Future<bool> deactivateService() async {
    serviceActive = false;
    bool result = await updateService();

    if (result) {
      await ForegroundService.stopForegroundService();
    }

    return result;
  }

  /// Private method to start foreground service with currently blocked app packages
  Future<void> _startForegroundServiceWithBlockedApps() async {
    if (appsBlocked != null && appsBlocked!.isNotEmpty) {
      List<String> packageNames = appsBlocked!
          .map((blockedApp) => blockedApp.packageName)
          .where((packageName) => packageName.isNotEmpty)
          .toList();

      if (packageNames.isNotEmpty) {
        // Call foreground service's restart function
      }
    }
  }
}

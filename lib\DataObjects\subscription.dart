import 'package:flutter/material.dart';
import 'package:v18ui/DataObjects/config_manager.dart';
import 'package:v18ui/DataObjects/config.dart';
import 'package:v18ui/Widgets/subscription_dialog.dart';

enum SubscriptionStatus {
  active,
  inactive,
  expired,
}

class Subscription {
  final String status;
  final String expiryDate;

  Subscription({
    required this.status,
    required this.expiryDate,
  });

  factory Subscription.fromJson(Map<String, dynamic> json) {
    return Subscription(
      status: json['status'],
      expiryDate: json['expiry_date'],
    );
  }

  Map<String, dynamic> toJson() => {
        'status': status,
        'expiry_date': expiryDate,
      };

  /// Check if subscription is active
  bool get isActive => status.toLowerCase() == 'active';

  /// Check if subscription is expired
  bool get isExpired => status.toLowerCase() == 'expired';

  /// Check if subscription is inactive
  bool get isInactive => status.toLowerCase() == 'inactive';

  /// Get subscription status as enum
  SubscriptionStatus get subscriptionStatus {
    switch (status.toLowerCase()) {
      case 'active':
        return SubscriptionStatus.active;
      case 'expired':
        return SubscriptionStatus.expired;
      case 'inactive':
      default:
        return SubscriptionStatus.inactive;
    }
  }

  /// Check subscription and show dialog if needed for a service
  /// Returns true if user can proceed, false if subscription is required
  Future<bool> checkSubscriptionAndShowDialog(
      BuildContext context, String serviceName) async {
    // Skip check for Focused YouTube as it's free
    if (serviceName.toLowerCase().contains('focused youtube')) {
      return true;
    }

    if (isActive) {
      return true;
    }

    // Show subscription dialog
    await _showSubscriptionDialog(context);
    return false;
  }

  /// Show subscription dialog based on current status
  Future<void> _showSubscriptionDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return SubscriptionDialog(
          subscriptionStatus: subscriptionStatus,
        );
      },
    );
  }

  /// Update subscription status and save to config
  /// This method updates both ConfigManager and saves to file
  static Future<bool> updateSubscriptionStatus(
      String newStatus, String newExpiryDate) async {
    try {
      final configManager = ConfigManager();
      final currentConfig = configManager.config;

      if (currentConfig == null) {
        print('❌ Config not found, cannot update subscription');
        return false;
      }

      // Create new subscription object
      final updatedSubscription = Subscription(
        status: newStatus,
        expiryDate: newExpiryDate,
      );

      // Create new config with updated subscription
      final updatedConfig = Config(
        userId: currentConfig.userId,
        isFirstRun: currentConfig.isFirstRun,
        subscription: updatedSubscription,
        isMasterFingerprintActivated:
            currentConfig.isMasterFingerprintActivated,
        configuredAppCount: currentConfig.configuredAppCount,
      );

      // Update ConfigManager
      configManager.config = updatedConfig;

      // Save to file
      await Config.saveConfig(updatedConfig);

      print('✅ Subscription status updated to: $newStatus');
      return true;
    } catch (e) {
      print('❌ Error updating subscription: $e');
      return false;
    }
  }

  /// Activate subscription
  static Future<bool> activateSubscription(String expiryDate) async {
    return await updateSubscriptionStatus('active', expiryDate);
  }

  /// Expire subscription
  static Future<bool> expireSubscription() async {
    return await updateSubscriptionStatus(
        'expired', DateTime.now().toIso8601String());
  }

  /// Deactivate subscription
  static Future<bool> deactivateSubscription() async {
    return await updateSubscriptionStatus(
        'inactive', DateTime.now().toIso8601String());
  }

  /// Get subscription message based on status
  String getSubscriptionMessage() {
    switch (subscriptionStatus) {
      case SubscriptionStatus.expired:
        return "Your subscription has expired, please re-activate your subscription";
      case SubscriptionStatus.inactive:
      default:
        return "Please activate your subscription to use this service";
    }
  }

  /// Get subscription dialog title based on status
  String getSubscriptionDialogTitle() {
    switch (subscriptionStatus) {
      case SubscriptionStatus.expired:
        return "Subscription Expired";
      case SubscriptionStatus.inactive:
      default:
        return "Subscription Required";
    }
  }
}

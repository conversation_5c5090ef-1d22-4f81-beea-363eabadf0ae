package com.kespra.v18.Utilities;

import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.provider.Settings;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.kespra.v18.services.ForegroundService;
import com.kespra.v18.services.OverlayService;
import com.kespra.v18.services.V18AccessibilityService;

import java.util.ArrayList;

public class SettingsBlockingUtils extends AppCompatActivity {
    private ActivityResultLauncher<Intent> accessibilityLauncher =
            registerForActivityResult(new ActivityResultContracts.StartActivityForResult(), result -> {
                if (!V18AccessibilityService.isAccessibilityServiceEnabled(getApplicationContext(), V18AccessibilityService.class)) {
                    OverlayService.accessibilitySettingsOverlay(getApplicationContext());
                }
                else
                {
                    /* If Accessibility service is enabled , then redirect the user back to settings page*/
                    Intent settings_intent = new Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS);
                    settings_intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    startActivity(settings_intent);
                }
            });

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        redirectAccessibility();
    }

    public void suspend_package(Context context, ArrayList<String> packageName)
    {
        /*Check if any foreground service is already running or not*/
        if(isForegroundServiceRunning(context,ForegroundService.class) == false)
        {
            Intent ForegroundIntent = new Intent(context, ForegroundService.class);
            ForegroundIntent.putStringArrayListExtra("packages",packageName);
            ForegroundIntent.putExtra("packages",packageName);
            context.startForegroundService(ForegroundIntent);
        }
        else {
            Intent serviceIntent = new Intent(context, ForegroundService.class);
            context.stopService(serviceIntent);
            suspend_package(context,packageName);
        }
    }

    public void unsuspend_package(Context context,String[] packageName) {

    }

    public boolean isForegroundServiceRunning(Context context , Class<?> serviceClass) {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        if (activityManager != null) {
            // Although you might see getRunningServices as depricated , it is actually still working but it only returns
            // activity results for our own app only.
            for (ActivityManager.RunningServiceInfo service : activityManager.getRunningServices(Integer.MAX_VALUE)) {
                if (serviceClass.getName().equals(service.service.getClassName())) {
                    System.out.println("Forefround service is running");
                    return true;
                }
            }
        }
        System.out.println("foreground service is not running");
        return false;
    }

    /*Events - If the user enters into Settings without Accessibility enabled , then the overlay will take over the screen.
    * Overlay redirects the user to accessibility Settings page , if the user enables accessibility settings then the user will be
    * sent back to Settings otherwise it will again show the overlay pop-up*/

    public void redirectAccessibility()
    {
        Intent accessibilityIntent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        accessibilityIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        accessibilityLauncher.launch(accessibilityIntent);
    }

}

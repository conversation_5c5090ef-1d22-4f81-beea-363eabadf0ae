# Payment System Implementation

This folder contains the complete UPI payment integration for the V18 app subscription system.

## Files Overview

### 1. `payment_models.dart`
- Contains data models for payment system
- `PaymentResult`: Handles payment response data
- `SubscriptionPlan`: Defines subscription plan structure
- `PaymentTransaction`: Tracks payment transactions

### 2. `upi_payment_service.dart`
- Core UPI payment service
- Handles UPI URL generation and app launching
- Payment verification (placeholder implementation)
- **TODO**: Replace placeholder values with actual payment details

### 3. `subscription_plans_service.dart`
- Manages available subscription plans
- Calculates savings and pricing
- **TODO**: Update with actual pricing and plans

### 4. `payment_screen.dart`
- Main payment selection screen
- Shows available subscription plans
- Handles plan selection and payment initiation

### 5. `payment_processing_screen.dart`
- Handles payment flow and status
- Shows payment progress and verification
- Updates subscription status on successful payment

## Configuration Required

### UPI Payment Service (`upi_payment_service.dart`)
Replace the following placeholders:

```dart
// Line 7-10: Replace with actual payment details
static const String _merchantId = "YOUR_MERCHANT_ID";
static const String _merchantName = "V18 App";
static const String _upiId = "your-upi-id@paytm"; // Replace with your UPI ID
static const String _transactionNote = "V18 App Subscription";
```

### Subscription Plans (`subscription_plans_service.dart`)
Update pricing in the `getAvailablePlans()` method:

```dart
// Lines 15-17, 35-37, 55-57: Update actual prices
price: 99.0, // TODO: Replace with actual price
price: 249.0, // TODO: Replace with actual price  
price: 799.0, // TODO: Replace with actual price
```

### Payment Verification
Implement actual payment verification in `upi_payment_service.dart`:

```dart
// Line 85-100: Replace with actual payment gateway integration
static Future<PaymentResult> verifyPayment(String transactionId) async {
  // TODO: Implement actual payment verification with your payment gateway
  // Call your backend API to verify the payment status
}
```

## How It Works

1. **User clicks "Activate" in subscription dialog**
   - `SubscriptionDialog` → `PaymentScreen`

2. **User selects a plan and clicks "Pay with UPI"**
   - `PaymentScreen` → `PaymentProcessingScreen`

3. **Payment processing flow:**
   - Generate unique order ID
   - Create UPI payment URL
   - Launch UPI app
   - User completes payment in UPI app
   - User returns and clicks "I have completed payment"
   - System verifies payment
   - Updates subscription status in config

4. **Subscription activation:**
   - Calls `Subscription.activateSubscription()`
   - Updates ConfigManager
   - Saves to config.json file

## Integration Points

### With Subscription System
- Uses `Subscription.activateSubscription()` to update status
- Integrates with `ConfigManager` for state management
- Follows the same pattern as `porn_deaddiction_service.dart`

### With Home Screen
- Subscription checking happens in `Subscription.checkSubscriptionAndShowDialog()`
- Shows payment dialog when subscription is required
- Focused YouTube remains free (no subscription check)

## Testing

1. **Test with inactive subscription:**
   - All services except Focused YouTube should show subscription dialog
   - Dialog should navigate to payment screen

2. **Test payment flow:**
   - Select a plan
   - Click "Pay with UPI"
   - Should open UPI app (if available)
   - Return and verify payment

3. **Test subscription activation:**
   - After successful payment, subscription should be active
   - All services should work without showing dialog

## Dependencies Added

- `url_launcher: ^6.2.4` - For launching UPI apps

## Security Notes

- Never store sensitive payment data in the app
- Always verify payments on the server side
- Use HTTPS for all payment-related API calls
- Implement proper error handling and logging

## Next Steps

1. Replace all TODO placeholders with actual values
2. Implement backend payment verification API
3. Add proper error handling and retry mechanisms
4. Test with real UPI apps and payment scenarios
5. Add analytics and logging for payment events

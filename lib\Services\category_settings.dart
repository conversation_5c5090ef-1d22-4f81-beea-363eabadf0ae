import 'dart:convert';

import 'package:v18ui/DataObjects/config_manager.dart';
import 'package:v18ui/DataObjects/file_paths.dart';
import 'package:v18ui/Services/category_config.dart';

class CategorySettings {
  bool weeklyOffAllowed;
  String? dailyAllowedTime;
  List<String> package;
  bool? timeoutOver;
  String? todayTotalWatchTime;
  String categoryName;
  String categoryType;
  DateTime last_track_date;
  List<DateTime> allowedDates;

  CategorySettings({
    required this.weeklyOffAllowed,
    this.dailyAllowedTime,
    required this.package,
    this.timeoutOver,
    this.todayTotalWatchTime,
    required this.categoryName,
    required this.categoryType,
    required this.last_track_date,
    this.allowedDates = const [],
  });

  factory CategorySettings.fromJson(Map<String, dynamic> json) {
    return CategorySettings(
      weeklyOffAllowed: json['weekly_off_allowed'],
      dailyAllowedTime: json['daily_allowed_time'],
      package: json['package'].cast<String>(),
      timeoutOver: json['timeout_over'],
      todayTotalWatchTime: json['todayTotalWatchTime'],
      categoryName: json['categoryName'],
      categoryType: json['categoryType'],
      last_track_date: json['last_track_date'],
      allowedDates: (json['allowedDates'] as List<dynamic>)
          .map((dateStr) => DateTime.parse(dateStr))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'weekly_off_allowed': weeklyOffAllowed,
        'daily_allowed_time': dailyAllowedTime,
        'package': package,
        'timeout_over': timeoutOver,
        'todayTotalWatchTime': todayTotalWatchTime,
        'categoryName': categoryName,
        'categoryType': categoryType,
        'last_track_date': last_track_date.toIso8601String(),
        'allowedDates':
            allowedDates.map((date) => date.toIso8601String()).toList(),
      };

/** We dont need shared preference , we will store our file in internal storage , this way we can read and modify files
 * from foreground service itself.
 */
  static Future<Map<String, CategorySettings>> loadCategoryConfigSettings(
      String jsonString) async {
    var settings_json = jsonDecode(jsonString);
    Map<String, CategorySettings> category_settings = {};
    for (final entry in settings_json.entries) {
      final categoryName = entry.key;
      final categoryData = entry.value;
      category_settings[categoryName] = CategorySettings.fromJson(categoryData);
    }
    return category_settings;
  }

  static Future<Map<String, dynamic>> UpdateSystemCategorySettings(
      CategorySettings config) async {
    ConfigManager().categorySettings![config.categoryName] = config;
    await CategoryConfig().saveCategoryConfig(
        FilePaths.categorySettings, ConfigManager().categorySettings!);
    return {"result": true, "reason": "Category updated successfully"};
  }

  /** Following function will be used to update user defined category settings */
  static Future<Map<String, dynamic>> UpdateUserCategorySettings(
      CategorySettings config) async {
    return {"result": true, "reason": "Not implemented yet"};
  }

  Future<Map<String, dynamic>> deleteUserCategorysettings(String key) async {
    if (key == 'System_generated') {
      return {
        "result": false,
        "reason": "System generated category cannot be deleted"
      };
    } else {
      return {"result": true, "reason": "Not implemented yet"};
    }
  }

  Future<Map<String, dynamic>> createUserCategorySettings(
      String key, CategorySettings config) async {
    return {"result": true, "reason": "Not implemented yet"};
  }
}

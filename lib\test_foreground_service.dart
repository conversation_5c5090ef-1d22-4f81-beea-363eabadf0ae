import 'package:flutter/material.dart';
import 'package:v18ui/Services/foreground_service.dart';
import 'package:v18ui/Services/block_apps_service.dart';
import 'package:v18ui/DataObjects/blocked_apps.dart';

/// Test screen to verify foreground service functionality
class TestForegroundServiceScreen extends StatefulWidget {
  const TestForegroundServiceScreen({Key? key}) : super(key: key);

  @override
  State<TestForegroundServiceScreen> createState() => _TestForegroundServiceScreenState();
}

class _TestForegroundServiceScreenState extends State<TestForegroundServiceScreen> {
  bool _serviceRunning = false;
  String _statusMessage = "Service not started";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Foreground Service Test'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      'Service Status',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _statusMessage,
                      style: TextStyle(
                        color: _serviceRunning ? Colors.green : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _startForegroundService,
              child: const Text('Start Foreground Service'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _stopForegroundService,
              child: const Text('Stop Foreground Service'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _testWithBlockedApps,
              child: const Text('Test with Sample Blocked Apps'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _testBlockAppsService,
              child: const Text('Test Block Apps Service Integration'),
            ),
            const SizedBox(height: 20),
            const Text(
              'Instructions:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              '1. Start the foreground service\n'
              '2. Check notification panel for "V18 protection is ON"\n'
              '3. Test with blocked apps to see if service monitors them\n'
              '4. Stop the service when done testing',
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _startForegroundService() async {
    try {
      bool result = await ForegroundService.startForegroundService();
      setState(() {
        _serviceRunning = result;
        _statusMessage = result ? "Service started successfully" : "Failed to start service";
      });
    } catch (e) {
      setState(() {
        _serviceRunning = false;
        _statusMessage = "Error starting service: $e";
      });
    }
  }

  Future<void> _stopForegroundService() async {
    try {
      bool result = await ForegroundService.stopForegroundService();
      setState(() {
        _serviceRunning = !result;
        _statusMessage = result ? "Service stopped successfully" : "Failed to stop service";
      });
    } catch (e) {
      setState(() {
        _statusMessage = "Error stopping service: $e";
      });
    }
  }

  Future<void> _testWithBlockedApps() async {
    try {
      // Test with some common apps that might be blocked
      List<String> testPackages = [
        'com.android.chrome',
        'com.google.android.youtube',
        'com.facebook.katana',
        'com.instagram.android',
      ];
      
      bool result = await ForegroundService.startForegroundService(
        blockPackages: testPackages,
      );
      
      setState(() {
        _serviceRunning = result;
        _statusMessage = result 
          ? "Service started with ${testPackages.length} blocked apps" 
          : "Failed to start service with blocked apps";
      });
    } catch (e) {
      setState(() {
        _serviceRunning = false;
        _statusMessage = "Error testing with blocked apps: $e";
      });
    }
  }

  Future<void> _testBlockAppsService() async {
    try {
      // Create a test BlockAppsService with sample blocked apps
      List<BlockedApp> testBlockedApps = [
        BlockedApp(
          packageName: 'com.android.chrome',
          appLabel: 'Chrome',
          blockingStartTime: '09:00',
          blockingEndTime: '17:00',
        ),
        BlockedApp(
          packageName: 'com.google.android.youtube',
          appLabel: 'YouTube',
          blockingStartTime: '09:00',
          blockingEndTime: '17:00',
        ),
      ];

      BlockAppsService blockService = BlockAppsService(
        appsBlocked: testBlockedApps,
        serviceActive: true,
      );

      // Test the activate service method which should start the foreground service
      bool result = await blockService.activateService();
      
      setState(() {
        _serviceRunning = result;
        _statusMessage = result 
          ? "Block Apps Service activated with foreground service" 
          : "Failed to activate Block Apps Service";
      });
    } catch (e) {
      setState(() {
        _serviceRunning = false;
        _statusMessage = "Error testing Block Apps Service: $e";
      });
    }
  }
}

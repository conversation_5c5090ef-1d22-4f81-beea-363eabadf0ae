package com.kespra.v18.Utilities;

import android.app.admin.DeviceAdminReceiver;
import android.app.admin.DevicePolicyManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;

import io.flutter.plugin.common.MethodChannel;

public class AdminReceiver extends DeviceAdminReceiver {

    public static MethodChannel.Result flutterResult;
    public boolean active_flag;


    @Override
    public void onEnabled(Context context, Intent intent) {
        super.onEnabled(context, intent);
        //There is an issue at this place , If the user enables device admin without using the apps intent then this
        //flutterResult is sending result to a null object , hence you will get error as null object reference.
        flutterResult.success(true);
        flutterResult = null;   // once success result is sent to flutter, old value is set to null
    }

    /*This solution is not much reliable because when the user clicks on deactivate , then this function is called.*/
    @Override
    public void onDisabled(Context context, Intent intent) {
        super.onDisabled(context, intent);
    }

    // We wont be
    public void showAdminDialog(Context context, ComponentName adminComponent) {

        // Alert dialog was used because it does not have cancel option , so you only have an option
        // to click on button and navigate to settings. however because user gives device admin access in the begning itself
        // and will not be able to remove later , as we are monitoring settings using accessibility , hence I think we can
        // replace AlertDialog and simply use the permission page which we have in app. Reason to remove AlertDialog is because
        // AlertDialog takes time to load (This behaviour was noticed in debug mode , however in real run it may vary.
        // Important - If the app is already given access , then the intent will not redirect the user to admin because
        // intent is ACTION_ADD_DEVICE_ADMIN .

        new AlertDialog.Builder(context)
                .setTitle("Admin access required")
                .setMessage("To protect you V18 needs admin access")
                .setPositiveButton("Enable", (dialog, which) -> {
                    Intent settings_intent = new Intent(DevicePolicyManager.ACTION_ADD_DEVICE_ADMIN);
                    settings_intent.putExtra(DevicePolicyManager.EXTRA_DEVICE_ADMIN, adminComponent);
                    settings_intent.putExtra(DevicePolicyManager.EXTRA_ADD_EXPLANATION, "This is extra explanation");
                    context.startActivity(settings_intent);
                })
                .setCancelable(false)
                .show();
    }



    // Following code checks whether overlay permissions are granted or not
    public boolean canDrawOverlays(Context context) {
        return Build.VERSION.SDK_INT < Build.VERSION_CODES.M || Settings.canDrawOverlays(context);
    }

    public void requestOverlayPermission(AppCompatActivity activity) {
        if (!canDrawOverlays(activity)) {
            Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    Uri.parse("package:" + activity.getPackageName()));
        }
    }
}

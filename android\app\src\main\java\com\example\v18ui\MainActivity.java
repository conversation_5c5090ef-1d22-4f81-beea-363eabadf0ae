package com.example.v18ui;

import android.content.Intent;
import android.content.pm.ApplicationInfo;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.embedding.android.FlutterFragmentActivity;
import io.flutter.plugin.common.MethodChannel;

import com.example.v18ui.permissions.PermissionManager;
import com.example.v18ui.services.ForegroundService;

public class MainActivity extends FlutterFragmentActivity {
    private static final String APP_DATA_CHANNEL = "AppHandler/AppData";

    @Override
    public void configureFlutterEngine(@NonNull FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);

        // Register the app data channel for backward compatibility
        new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), APP_DATA_CHANNEL)
                .setMethodCallHandler(
                        (call, result) -> {
                            switch(call.method) {
                                case "getAppCount":
                                    int app_count = AppData.getAppCount(getApplicationContext());
                                    result.success(app_count);
                                    break;
                                case "AppList":
                                    List<Map<String,Object>> app_data = AppData.get_Installed_apps(getApplicationContext());
                                    result.success(app_data);
                                    break;
                                case "getIcon":
                                    byte[] app_icon = AppData.get_icon(getApplicationContext().getPackageManager(),call.argument("package_name"));
                                    result.success(app_icon);
                                    break;
                                // Permission methods
                                case "isAccessibilityServiceEnabled":
                                    result.success(Permissions_Settings.isAccessibilityEnabled(this));
                                    break;
                                case "isUsageStatsEnabled":
                                    result.success(Permissions_Settings.isUsageStatsPermissionGranted(this));
                                    break;
                                case "isDeviceAdminEnabled":
                                    result.success(Permissions_Settings.isDeviceAdminActive(this));
                                    break;
                                case "openAccessibilitySettings":
                                    Permissions_Settings.openAccessibilitySettings(this);
                                    result.success(null);
                                    break;
                                case "openUsageStatsSettings":
                                    Permissions_Settings.openUsageStatsSettings(this);
                                    result.success(null);
                                    break;
                                case "openDeviceAdminSettings":
                                    Permissions_Settings.openDeviceAdminSettings(this);
                                    result.success(null);
                                    break;
                                case "getInstalledBrowsers":
                                    List<String> browsers = AppData.getInstalledBrowsers(getApplicationContext());
                                    result.success(browsers);
                                    break;
                                // Foreground Service methods
                                case "startForegroundService":
                                    ArrayList<String> monitorPackages = call.argument("packages");
                                    Intent serviceIntent = new Intent(this, ForegroundService.class);
                                    if (monitorPackages != null) {
                                        serviceIntent.putStringArrayListExtra("packages", monitorPackages);
                                    }
                                    ContextCompat.startForegroundService(this, serviceIntent);
                                    result.success(true);
                                    break;
                                case "stopForegroundService":
                                    Intent stopServiceIntent = new Intent(this, ForegroundService.class);
                                    stopService(stopServiceIntent);
                                    result.success(true);
                                    break;
                                default:
                                    result.notImplemented();
                                    break;
                            }
                        });

        // Register the permission manager
        // Pass 'this' (the Activity) instead of getApplicationContext() to allow proper activity launching
        PermissionManager.registerWith(flutterEngine, this);
    }
}
package com.kespra.v18.services;

import android.accessibilityservice.AccessibilityService;
import android.accessibilityservice.AccessibilityServiceInfo;
import android.content.Context;
import android.content.pm.ServiceInfo;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityManager;

import java.util.List;

public class V18AccessibilityService extends AccessibilityService{

    @Override
    public void onAccessibilityEvent(AccessibilityEvent event) {
        if (event.getEventType() == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED) {
            String currentPackageName = event.getPackageName().toString();
            String currentClassName = event.getClassName().toString();

            // Check if user is in the Device Admin settings
            if (currentClassName.contains("com.android.settings.DeviceAdminSettings")
                    || currentClassName.contains("com.android.settings.Settings$UsageAccessSettingsActivity")
                    || currentClassName.contains("com.android.settings.Settings$ManageOverlayPermissionActivity")
            ) {
                OverlayService.settingsRestrictionsOverlay(getApplicationContext());
            }
        }
    }

    @Override
    public void onInterrupt() {

    }

    public static boolean isAccessibilityServiceEnabled(Context context, Class<? extends AccessibilityService> serviceClass) {
        AccessibilityManager accessibilityManager =
                (AccessibilityManager) context.getSystemService(Context.ACCESSIBILITY_SERVICE);

        List<AccessibilityServiceInfo> enabledServices =
                accessibilityManager.getEnabledAccessibilityServiceList(AccessibilityServiceInfo.FEEDBACK_ALL_MASK);

        for (AccessibilityServiceInfo serviceInfo : enabledServices) {
            ServiceInfo info = serviceInfo.getResolveInfo().serviceInfo;
            if (info.packageName.equals(context.getPackageName()) &&
                    info.name.equals(serviceClass.getName())) {
                return true;  // Accessibility service is enabled
            }
        }
        return false;  // Accessibility service is not enabled
    }
}

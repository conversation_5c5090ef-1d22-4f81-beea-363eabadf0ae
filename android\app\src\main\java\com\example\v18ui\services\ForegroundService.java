package com.example.v18ui.services;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.kespra.v18.R;

import java.util.ArrayList;

public class ForegroundService extends Service {

    private Handler handler;
    private Runnable runnable;
    private long CHECK_INTERVAL=2000;

    Context context;

    private static final String CHANNEL_ID = "ForegroundServiceChannel";

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        context = getApplicationContext();
        createNotificationChannel(); // Create the channel for the notification (required for Android 8.0+)

    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        handler.removeCallbacks(runnable);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {

        NotificationCompat.Builder builder = new NotificationCompat.Builder(ForegroundService.this, CHANNEL_ID)
                // If you remove this Icon , then foreground service is not visible
                .setSmallIcon(R.drawable.app_logo)
                .setContentTitle("V18 protection is ON")
                .setContentText("Stay focused on your goals , V18 is there to protect you")
                .setPriority(NotificationCompat.PRIORITY_MAX)
                .setCategory(NotificationCompat.CATEGORY_SERVICE)
                .setOngoing(true);

        Notification notification = builder.build();
        startForeground(1,notification);

        ArrayList<String> block_packages = intent.getStringArrayListExtra("packages"); // Retrieve data passed to the service

        handler = new Handler();
        runnable = new Runnable() {
            @Override
            public void run() {
                String foreground_app = UsageStatsService.checkForegroundApp(context);
                System.out.println("foreground app detected - "+foreground_app);
                if(block_packages.contains(foreground_app))
                {
                    // After the home intent is called , try to include screen overlay to show motivation msg to the user
                    Intent homeIntent = new Intent(Intent.ACTION_MAIN);
                    homeIntent.addCategory(Intent.CATEGORY_HOME);
                    homeIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    startActivity(homeIntent);
                }
                if(foreground_app.contains("com.android.settings"))
                {
                    //checking if Accessibility service is running or not , if it is not running then the user is prompted to enable the service
                    if(!V18AccessibilityService.isAccessibilityServiceEnabled(context,V18AccessibilityService.class))
                    {
                        OverlayService.accessibilitySettingsOverlay(context);
                    }
                }
                handler.postDelayed(this, CHECK_INTERVAL);
            }
        };

        handler.post(runnable);

        return START_REDELIVER_INTENT; // Options: START_STICKY, START_NOT_STICKY, START_REDELIVER_INTENT
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel serviceChannel = new NotificationChannel(
                    CHANNEL_ID,
                    "Foreground Service Channel",
                    NotificationManager.IMPORTANCE_DEFAULT);

            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(serviceChannel);
            }
        }
    }


}

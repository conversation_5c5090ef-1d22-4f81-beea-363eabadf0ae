package com.example.v18ui.services;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Binder;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.example.v18ui.R;
import com.kespra.v18.services.UsageStatsService;
import com.example.v18ui.services.OverlayService;
import com.example.v18ui.permissions.AccessibilityPermission;

import java.sql.Time;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import java.util.TimeZone;

public class ForegroundService extends Service {

    private Handler handler;
    private Runnable runnable;
    private long CHECK_INTERVAL=2000;
    Context context;
    private static final String CHANNEL_ID = "ForegroundServiceChannel";
    // Map to track time spent in each app
    private Map<String, Long> appUsageTime = new HashMap<>();
    // Last detected foreground app
    private String lastForegroundApp = "";
    // Last time we checked
    private long lastCheckTime = System.currentTimeMillis();

    // Binder for clients to access this service
    private final IBinder binder = (IBinder) new LocalBinder();
    
    // List to store active blocking configs
    private List<BlockingConfig> activeBlockingConfigs = new ArrayList<>();
    
    /**
     * Class for clients to access this service
     */
    public class LocalBinder extends Binder {
        ForegroundService getService() {
            return ForegroundService.this;
        }
    }
    
    @Override
    public IBinder onBind(Intent intent) {
        return binder;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        context = getApplicationContext();
        createNotificationChannel(); // Create the channel for the notification (required for Android 8.0+)

    }


    @Override
    public void onDestroy() {
        super.onDestroy();
        handler.removeCallbacks(runnable);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {

        NotificationCompat.Builder builder = new NotificationCompat.Builder(ForegroundService.this, CHANNEL_ID)
                // If you remove this Icon , then foreground service is not visible
                .setSmallIcon(R.mipmap.ic_launcher)
                .setContentTitle("V18 protection is ON")
                .setContentText("Stay focused on your goals , V18 is there to protect you")
                .setPriority(NotificationCompat.PRIORITY_MAX)
                .setCategory(NotificationCompat.CATEGORY_SERVICE)
                .setOngoing(true);

        Notification notification = builder.build();
        startForeground(1,notification);

        ArrayList<String> monitor_packages = intent.getStringArrayListExtra("packages");
        List<BlockingConfig> blockingConfigs = new ArrayList<>();
        
        // Parse blocking configs from intent if available
        if (intent.hasExtra("blockingConfigs")) {
            try {
                String blockingConfigsJson = intent.getStringExtra("blockingConfigs");
                blockingConfigs = parseBlockingConfigsJson(blockingConfigsJson);
                
                // Store the active configs
                activeBlockingConfigs = blockingConfigs;
                
                System.out.println("Loaded " + blockingConfigs.size() + " blocking configs");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        handler = new Handler();
        runnable = new Runnable() {
            @Override
            public void run() {
                String foreground_app = UsageStatsService.checkForegroundApp(context);
                System.out.println("foreground app detected - "+foreground_app);

                long currentTime = System.currentTimeMillis();
                long elapsedTime = currentTime - lastCheckTime;

                // If we had a previous app and it's different from current one
                if (!lastForegroundApp.isEmpty() && !lastForegroundApp.equals(foreground_app)) {
                    // Save the time spent in the previous app
                    Long previousTime = appUsageTime.get(lastForegroundApp);
                    if (previousTime == null) {
                        previousTime = 0L;
                    }
                    appUsageTime.put(lastForegroundApp, previousTime + elapsedTime);
                }

                // Update tracking variables
                lastForegroundApp = foreground_app;
                lastCheckTime = currentTime;

                // Check if current app is in monitored packages
                if(monitor_packages != null && monitor_packages.contains(foreground_app)) {
                    // Find the corresponding BlockingConfig
                    for (BlockingConfig config : blockingConfigs) {
                        if (config.packages.contains(foreground_app)) {
                            if (!config.timeoutOver) {
                                // Get current usage time
                                Long currentUsage = appUsageTime.get(foreground_app);
                                if (currentUsage == null) {
                                    currentUsage = 0L;
                                }

                                // Update todayTotalWatchTime
                                long totalMinutes = currentUsage / 60000; // Convert ms to minutes
                                config.todayTotalWatchTime = String.format("%d:%02d",
                                        totalMinutes / 60, totalMinutes % 60);

                                // Check if daily limit reached
                                try {
                                    SimpleDateFormat format = new SimpleDateFormat("HH:mm");
                                    Date currentWatchTime = format.parse(config.todayTotalWatchTime);
                                    Date allowedTime = format.parse(config.dailyAllowedTime.toString());

                                    if (currentWatchTime.compareTo(allowedTime) >= 0) {
                                        // Time limit reached, block the app
                                        config.timeoutOver = true;
                                        blockApp(foreground_app);
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            } else {
                                // Already timed out, block immediately
                                blockApp(foreground_app);
                            }
                            break;
                        }
                    }
                }

                // Check for accessibility settings
                if(foreground_app.contains("com.android.settings")) {
                    AccessibilityPermission accessibilityPermission = new AccessibilityPermission(context);
                    if(!accessibilityPermission.isPermissionGranted()) {
                        OverlayService.accessibilitySettingsOverlay(context);
                    }
                }

                handler.postDelayed(this, CHECK_INTERVAL);
            }
        };

        handler.post(runnable);
        return START_REDELIVER_INTENT;
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel serviceChannel = new NotificationChannel(
                    CHANNEL_ID,
                    "Foreground Service Channel",
                    NotificationManager.IMPORTANCE_DEFAULT);

            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(serviceChannel);
            }
        }
    }

    private void blockApp(String packageName) {
        // Launch blocking overlay or redirect to home screen
        Intent homeIntent = new Intent(Intent.ACTION_MAIN);
        homeIntent.addCategory(Intent.CATEGORY_HOME);
        homeIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(homeIntent);

        // Show blocking overlay
        OverlayService.showMotivationOverlay(context);
    }

    private class BlockingConfig
    {
        Boolean weeklyOffAllowed;
        Date dailyAllowedTime;
        List<String> packages;
        Boolean timeoutOver;
        String todayTotalWatchTime;
        String categoryName;
        String categoryType;
        Date last_track_date;
        List<Date> allowedDates;

        public BlockingConfig(Boolean weeklyOffAllowed, Date dailyAllowedTime, List<String> packages, Boolean timeoutOver, String todayTotalWatchTime, String categoryName, String categoryType, Date last_track_date, List<Date> allowedDates) {
            this.weeklyOffAllowed = weeklyOffAllowed;
            this.dailyAllowedTime = dailyAllowedTime;
            this.packages = packages;
            this.timeoutOver = timeoutOver;
            this.todayTotalWatchTime = todayTotalWatchTime;
            this.categoryName = categoryName;
            this.categoryType = categoryType;
            this.last_track_date = last_track_date;
            this.allowedDates = allowedDates;
        }
    }
    
    /**
     * Parse blocking configs from JSON string
     */
    private List<BlockingConfig> parseBlockingConfigsJson(String json) {
        List<BlockingConfig> configs = new ArrayList<>();
        
        try {
            JSONArray configsArray = new JSONArray(json);
            
            for (int i = 0; i < configsArray.length(); i++) {
                JSONObject configObj = configsArray.getJSONObject(i);
                
                // Parse weekly_off_allowed
                boolean weeklyOffAllowed = configObj.optBoolean("weekly_off_allowed", false);
                
                // Parse daily_allowed_time
                String dailyAllowedTimeStr = configObj.optString("daily_allowed_time", "00:00");
                SimpleDateFormat format = new SimpleDateFormat("HH:mm");
                Date dailyAllowedTime = format.parse(dailyAllowedTimeStr);
                
                // Parse package list
                JSONArray packagesArray = configObj.getJSONArray("package");
                List<String> packages = new ArrayList<>();
                for (int j = 0; j < packagesArray.length(); j++) {
                    packages.add(packagesArray.getString(j));
                }
                
                // Parse timeout_over
                boolean timeoutOver = configObj.optBoolean("timeout_over", false);
                
                // Parse todayTotalWatchTime
                String todayTotalWatchTime = configObj.optString("todayTotalWatchTime", "00:00");
                
                // Parse categoryName
                String categoryName = configObj.optString("categoryName", "");
                
                // Parse categoryType
                String categoryType = configObj.optString("categoryType", "");
                
                // Parse last_track_date
                String lastTrackDateStr = configObj.optString("last_track_date", "");
                Date lastTrackDate = new Date();
                if (!lastTrackDateStr.isEmpty()) {
                    try {
                        SimpleDateFormat isoFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
                        isoFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
                        lastTrackDate = isoFormat.parse(lastTrackDateStr);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                // Create BlockingConfig object and add to list
                BlockingConfig config = new BlockingConfig(
                    weeklyOffAllowed,
                    dailyAllowedTime,
                    packages,
                    timeoutOver,
                    todayTotalWatchTime,
                    categoryName,
                    categoryType,
                    lastTrackDate,
                    new ArrayList<>() // allowedDates - not implemented yet
                );

                configs.add(config);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return configs;
    }
    
    /**
     * Get active blocking configs as JSON string
     */
    public String getActiveBlockingConfigsJson() {
        JSONArray configsArray = new JSONArray();
        
        try {
            for (BlockingConfig config : activeBlockingConfigs) {
                JSONObject configObj = new JSONObject();
                
                configObj.put("weekly_off_allowed", config.weeklyOffAllowed);
                
                SimpleDateFormat format = new SimpleDateFormat("HH:mm");
                configObj.put("daily_allowed_time", format.format(config.dailyAllowedTime));
                
                JSONArray packagesArray = new JSONArray();
                for (String pkg : config.packages) {
                    packagesArray.put(pkg);
                }
                configObj.put("package", packagesArray);
                
                configObj.put("timeout_over", config.timeoutOver);
                configObj.put("todayTotalWatchTime", config.todayTotalWatchTime);
                configObj.put("categoryName", config.categoryName);
                configObj.put("categoryType", config.categoryType);
                
                SimpleDateFormat isoFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
                isoFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
                configObj.put("last_track_date", isoFormat.format(config.last_track_date));
                
                // Add allowedDates array
                JSONArray allowedDatesArray = new JSONArray();
                for (Date date : config.allowedDates) {
                    allowedDatesArray.put(isoFormat.format(date));
                }
                configObj.put("allowedDates", allowedDatesArray);
                
                configsArray.put(configObj);
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        
        return configsArray.toString();
    }
}

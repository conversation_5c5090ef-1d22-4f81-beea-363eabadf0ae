import 'dart:convert';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:v18ui/DataObjects/app_data.dart';
import 'package:v18ui/DataObjects/file_paths.dart';
import 'package:v18ui/Services/category_settings.dart';
import 'package:v18ui/Services/gpt_service.dart';

class CategoryConfig {
  /** This function is called only once during first run of the app */
  Future<Map<String, CategorySettings>> defaultCategorySettings() async {
    final String commonCategorisedAppsString =
        await rootBundle.loadString(FilePaths.common_categorised_apps);
    await AppData.load_all_packages();
    Map<String, dynamic> decodedMap =
        (jsonDecode(commonCategorisedAppsString))['SystemCategorised'];
    Map<String, List<String>> commonCategorydata = decodedMap.map(
      (key, value) => MapEntry(key, List<String>.from(value)),
    );
    dynamic defaultCategorySettings = await _categorizeApps(
        AppData.all_packages, Map.from(commonCategorydata));
    saveCategoryConfig(FilePaths.categorySettings, defaultCategorySettings);
    return defaultCategorySettings;
  }

  Future<Map<String, CategorySettings>> _categorizeApps(
    List<String> installedPackages,
    Map<String, List<String>> predefinedCategories,
  ) async {
    final Map<String, CategorySettings> result = {};
    final List<String> uncategorized = [];

    // Filter web browsers
    List<String> installedBrowsers = await AppData.getInstalledBrowsers();

    // Step 1: Create the "Browsers" category if browsers are installed
    if (installedBrowsers.isNotEmpty) {
      result["Browsers"] = CategorySettings(
        last_track_date: DateTime.now(),
        dailyAllowedTime: "",
        package: installedBrowsers,
        timeoutOver: false,
        todayTotalWatchTime: "",
        categoryName: "Browsers",
        categoryType: "System_generated",
        weeklyOffAllowed: false,
      );
    }

    // Step 2: Process each predefined category
    for (var entry in predefinedCategories.entries) {
      final categoryName = entry.key;
      final packageList = entry.value;

      // Create a list to store installed packages for this category
      final List<String> installedCategoryPackages = [];

      // Check which packages from this category are installed
      for (var pkg in packageList) {
        if (installedPackages.contains(pkg)) {
          installedCategoryPackages.add(pkg);
        }
      }

      // Only create the category if it has installed packages
      if (installedCategoryPackages.isNotEmpty) {
        result[categoryName] = CategorySettings(
          last_track_date: DateTime.now(),
          dailyAllowedTime: "",
          package: installedCategoryPackages,
          timeoutOver: false,
          todayTotalWatchTime: "",
          categoryName: categoryName,
          categoryType: "System_generated",
          weeklyOffAllowed: false,
        );
      }
    }

    // Step 3: Find uncategorized apps
    // An app is uncategorized if it's not in any category except PornDeaddictionDisabledApps
    // (since apps in PornDeaddictionDisabledApps can also be in other categories)
    Set<String> categorizedApps = Set<String>();

    for (var category in result.entries) {
      if (category.key != "PornDeaddictionDisabledApps") {
        categorizedApps.addAll(category.value.package);
      }
    }

    for (var pkg in installedPackages) {
      if (!categorizedApps.contains(pkg)) {
        uncategorized.add(pkg);
      }
    }

    // Step 4: Ask GPT to classify uncategorized packages
    // This part is commented out to save GPT credits, but the implementation remains

    /*if (uncategorized.isNotEmpty) {
    final gptResults = await findCategory(uncategorized);

    for (var entry in gptResults.entries) {
      result.putIfAbsent(
          entry.key,
          () => CategorySettings(
                dailyAllowedTime: "",
                package: [],
                timeoutOver: false,
                todayTotalWatchTime: "",
                categoryName: entry.key,
                categoryType: "System_generated",
                weeklyOffAllowed: false,
              ));
      result[entry.key]!.package.addAll(entry.value);
    }
  }*/

    return result;
  }

  Future<Map<String, List<String>>> findCategory(
      List<String> unCategorisedApps) async {
    final gpt = GPTService(
        // remove it from here
        apiKey:
            "********************************************************************************************************************************************************************");

    final result = await gpt.categorizePackages(unCategorisedApps);

    print(result);
    return result;
  }

  /** Used to create custom blocking category */
  addCategory(String packageName) {}

  /** Only user defined categories can be deleted */
  deleteCategory(String packageName) {}

  saveCategoryConfig(
      String filepath, Map<String, CategorySettings> categorySettings) async {
    String jsonString = json.encode(categorySettings);
    File categorySettingsFile = File(filepath);
    await categorySettingsFile.writeAsString(jsonString);
    print("✅ Category config file updated!");
  }
}

package com.kespra.v18.Data;

import java.util.HashSet;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class DomainFetcher {
    private static final String DOMAINS_URL = "http://192.168.1.5:5000/get_domains/";

    public static HashSet<String> fetchDomains() {
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(DOMAINS_URL)
                .addConverterFactory(GsonConverterFactory.create())
                .build();

        ApiService apiService = retrofit.create(ApiService.class);
        Call<ResponseBody> call = apiService.getBlockedDomain();
        call.enqueue(new Callback<ResponseBody>() {
            @Override
            public void onResponse(Call<ResponseBody> call, Response<ResponseBody> response) {
                if (response.isSuccessful()) {
                    // Handle the API response on the UI thread
                    ResponseBody responseData = response.body();
                }
            }

            @Override
            public void onFailure(Call<ResponseBody> call, Throwable t) {
                t.printStackTrace(); // Log error
            }
        });
        return new HashSet<>();
    }
}

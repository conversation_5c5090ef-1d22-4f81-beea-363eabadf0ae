package com.kespra.v18.Utilities;

import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.net.Uri;

import com.kespra.v18.Data.DomainFetcher;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class WebBlockingUtils {
    Set<String> blockedDomains;
    private WebBlockingUtils()
    {
        this.blockedDomains = DomainFetcher.fetchDomains();
        System.out.println("Following are the blocked domains" + blockedDomains.toArray());
    }

    public static WebBlockingUtils startWebMonitoring()
    {
        return new WebBlockingUtils();
    }

    public boolean isUnsafeWebsite(String url) {
        for (String domain : blockedDomains) {
            if (url.contains(domain)) {
                return true;
            }
        }
        return false;
    }

    public List<String> getInstalledBrowsers(PackageManager packageManager) {
        List<String> browserList = new ArrayList<>();

        // Making google as the default browser for opening intents
        Intent browserIntent = new Intent(Intent.ACTION_VIEW);
        browserIntent.addCategory(Intent.CATEGORY_BROWSABLE);
        browserIntent.setData(android.net.Uri.parse("http://www.google.com"));

        // Get apps that can handle the intent
        List<ResolveInfo> resolveInfoList = packageManager.queryIntentActivities(browserIntent, 0);

        for (ResolveInfo resolveInfo : resolveInfoList) {
            String packageName = resolveInfo.activityInfo.packageName;
            browserList.add(packageName);
        }
        return browserList;
    }

    // Following method will extract domain name the user is visiting
    private static String extractDomain(String url) {
        try {
            Uri uri = Uri.parse(url);
            String host = uri.getHost(); // Extracts domain like "example.com"
            if (host != null && host.startsWith("www.")) {
                host = host.substring(4); // Remove "www."
            }
            return host;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public boolean isSafeUrl(String url)
    {

        return false;
    }

}

import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:v18ui/Services/category_settings.dart';

/// Service class to handle foreground service operations.
/// This service manages the Android foreground service that monitors app usage
/// and ensures accessibility services remain active.
class ForegroundService {
  static const MethodChannel _channel = MethodChannel('AppHandler/AppData');

  /// Private method to start the foreground service with the specified list of packages to block.
  ///
  static Future<List<dynamic>> _startForegroundService(
      List<CategorySettings> blockCategories) async {
    try {
      // Collect all package names from all categories
      List<String> allPackages = [];
      for (var category in blockCategories) {
        allPackages.addAll(category.package);
      }

      // Remove duplicates
      allPackages = allPackages.toSet().toList();

      // Convert CategorySettings list to JSON
      String blockingConfigsJson =
          jsonEncode(blockCategories.map((config) => config.toJson()).toList());

      // Start the foreground service with the packages and blocking configs
      final bool result = await _channel.invokeMethod('startForegroundService',
          {'packages': allPackages, 'blockingConfigs': blockingConfigsJson});

      print(
          'Foreground service started with ${allPackages.length} packages and ${blockCategories.length} configs');
      return [true, "Success"];
    } catch (e) {
      print('Error starting foreground service: $e');
      return [false, "Unable to start foreground service"];
    }
  }

  static Future<List<dynamic>> stopForegroundService() async {
    try {
      final bool result = await _channel.invokeMethod('stopForegroundService');
      print('Foreground service stopped: $result');
      return [true, "Success"];
    } catch (e) {
      print('Error stopping foreground service: $e');
      return [false, "Unable to stop foreground service"];
    }
  }

  /// Checks if the foreground service is currently running.
  /// Note: This method would need to be implemented on the Android side
  /// This is only created if you want to check the service status from Flutter.
  static Future<bool> isForegroundServiceRunning() async {
    try {
      // This method would need to be implemented in MainActivity.java
      final bool result =
          await _channel.invokeMethod('isForegroundServiceRunning');
      return result;
    } catch (e) {
      print('Error checking foreground service status: $e');
      return false;
    }
  }

  /// Gets the current active blocking configurations from the running service
  static Future<List<CategorySettings>> getActiveBlockingConfigs() async {
    try {
      final String configsJson =
          await _channel.invokeMethod('getActiveBlockingConfigs');
      if (configsJson.isNotEmpty) {
        List<dynamic> configsList = jsonDecode(configsJson);
        return configsList
            .map((config) => CategorySettings.fromJson(config))
            .toList();
      }
      return [];
    } catch (e) {
      print('Error getting active blocking configs: $e');
      return [];
    }
  }

  /// Restarts the foreground service with new block packages.
  /// First checks if the service is running. If not running, just starts it.
  /// If running, stops it first and then starts with new packages.
  static Future<bool> restartForegroundService(
      CategorySettings newCategorySettings) async {
    try {
      // Check if service is currently running
      bool isRunning = await isForegroundServiceRunning();

      // Prepare the list of category settings to use
      List<CategorySettings> categorySettingsToUse = [];

      if (isRunning) {
        // Service is running, get current configs from the service
        List<CategorySettings> currentConfigs =
            await getActiveBlockingConfigs();
        categorySettingsToUse = List.from(currentConfigs);

        // Update or add the new category settings
        bool categoryExists = false;
        for (int i = 0; i < categorySettingsToUse.length; i++) {
          if (categorySettingsToUse[i].categoryName ==
              newCategorySettings.categoryName) {
            categorySettingsToUse[i] = newCategorySettings;
            categoryExists = true;
            break;
          }
        }

        if (!categoryExists) {
          categorySettingsToUse.add(newCategorySettings);
        }

        await stopForegroundService();
        // Add a small delay to ensure the service is fully stopped
        await Future.delayed(const Duration(milliseconds: 500));
      } else {
        print('Service is not running, starting directly...');
        // Just use the new category settings
        categorySettingsToUse.add(newCategorySettings);
      }

      // Start the service with all category settings
      await _startForegroundService(categorySettingsToUse);
      return true;
    } catch (e) {
      print('Error restarting foreground service: $e');
      return false;
    }
  }
}

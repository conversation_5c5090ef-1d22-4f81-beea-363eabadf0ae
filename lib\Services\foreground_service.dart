import 'package:flutter/services.dart';

/// Service class to handle foreground service operations.
/// This service manages the Android foreground service that monitors app usage
/// and ensures accessibility services remain active.
class ForegroundService {
  static const MethodChannel _channel = MethodChannel('AppHandler/AppData');

  /// Starts the foreground service with the specified list of packages to block.
  ///
  /// [blockPackages] - List of package names that should be blocked/monitored
  /// Returns true if the service was started successfully, false otherwise.
  static Future<bool> startForegroundService(
      {List<String>? blockPackages}) async {
    try {
      final bool result =
          await _channel.invokeMethod('startForegroundService', {
        'packages': blockPackages ?? [],
      });
      print('Foreground service started: $result');
      return result;
    } catch (e) {
      print('Error starting foreground service: $e');
      return false;
    }
  }

  static updateForegroundService() {}

  /// Stops the foreground service.
  ///
  /// Returns true if the service was stopped successfully, false otherwise.
  static Future<bool> stopForegroundService() async {
    try {
      final bool result = await _channel.invokeMethod('stopForegroundService');
      print('Foreground service stopped: $result');
      return result;
    } catch (e) {
      print('Error stopping foreground service: $e');
      return false;
    }
  }

  /// Checks if the foreground service is currently running.
  /// Note: This method would need to be implemented on the Android side
  /// This is only created if you want to check the service status from Flutter.
  static Future<bool> isForegroundServiceRunning() async {
    try {
      // This method would need to be implemented in MainActivity.java
      final bool result =
          await _channel.invokeMethod('isForegroundServiceRunning');
      return result;
    } catch (e) {
      print('Error checking foreground service status: $e');
      return false;
    }
  }

  /// Restarts the foreground service with new block packages.
  /// This is a convenience method that stops and then starts the service.
  static Future<bool> restartForegroundService(
      {List<String>? blockPackages}) async {
    try {
      await stopForegroundService();
      // Add a small delay to ensure the service is fully stopped
      await Future.delayed(const Duration(milliseconds: 500));
      return await startForegroundService(blockPackages: blockPackages);
    } catch (e) {
      print('Error restarting foreground service: $e');
      return false;
    }
  }
}

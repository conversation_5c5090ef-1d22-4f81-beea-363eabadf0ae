import 'package:v18ui/DataObjects/config_manager.dart';
import 'package:v18ui/Services/Service.dart';
import 'package:v18ui/Services/category_settings.dart';
import 'package:v18ui/Services/foreground_service.dart';

class QuitGamblingService {
  bool serviceActive;
  final bool userGenerated = false;

  QuitGamblingService({required this.serviceActive});

  factory QuitGamblingService.fromJson(Map<String, dynamic> json) {
    return QuitGamblingService(serviceActive: json['serviceActive'] ?? false);
  }

  Map<String, dynamic> toJson() {
    return {
      'serviceActive': serviceActive,
    };
  }

  Future<bool> updateService() async {
    return await Service().updateService('quitGamblingService', toJson());
  }

  /** We want to block gambling related apps completely as this is also a deaddiction service */

  Future<List<dynamic>> startService() async {
    await updateService();
    CategorySettings settings = ConfigManager().categorySettings!['Gambling']!;
    settings.timeoutOver = true;
    settings.todayTotalWatchTime = "01:00";
    settings.last_track_date = DateTime.now();
    settings.dailyAllowedTime = "00:00";
    settings.weeklyOffAllowed = false;
    await CategorySettings.UpdateSystemCategorySettings(settings);
    await ForegroundService.restartForegroundService(settings);

    return [true, "Success"];
  }

  Future<List<dynamic>> stopService() async {
    await updateService();
    return [true, "Success"];
  }
}

package com.kespra.v18.services;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.content.Intent;
import android.graphics.PixelFormat;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.os.Handler;

import com.kespra.v18.Utilities.SettingsBlockingUtils;
import com.kespra.v18.R;

public class OverlayService {

    private static WindowManager windowManager;

    private static View overlayView;

    public static void accessibilitySettingsOverlay(Context context) {
        if (overlayView != null) return; // Prevent multiple overlays

        Context appContext = context.getApplicationContext();
        // Initialize WindowManager
        windowManager = (WindowManager) appContext.getSystemService(Context.WINDOW_SERVICE);

        // Inflate the overlay layout
        LayoutInflater inflater = LayoutInflater.from(appContext);
        overlayView = inflater.inflate(R.layout.accessibility_overlay, null);

        // Configure layout parameters
        WindowManager.LayoutParams params = new WindowManager.LayoutParams(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                PixelFormat.OPAQUE);

        params.gravity = Gravity.TOP | Gravity.CENTER_HORIZONTAL;

        // Add the overlay view to the WindowManager
        windowManager.addView(overlayView, params);

        // Handle button click in the overlay
        Button enableButton = overlayView.findViewById(R.id.enable_accessibility_button);
        enableButton.setOnClickListener(v -> {
            // Redirect to Accessibility settings
            Intent blockingUtil = new Intent(context, SettingsBlockingUtils.class);
            blockingUtil.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(blockingUtil);
            removeOverlay();
        });
    }

    public static void settingsRestrictionsOverlay(Context context) {
        if (overlayView != null) return; // Prevent multiple overlays

        // Initialize WindowManager
        windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);

        // Inflate the overlay layout
        LayoutInflater inflater = LayoutInflater.from(context);
        overlayView = inflater.inflate(R.layout.settings_restriction_overlay, null);

        // Configure layout parameters
        WindowManager.LayoutParams params = new WindowManager.LayoutParams(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                PixelFormat.OPAQUE);

        params.gravity = Gravity.TOP | Gravity.CENTER_HORIZONTAL;

        // Add the overlay view to the WindowManager
        windowManager.addView(overlayView, params);

        // after 2 seconds remove the overlay
        new Handler().postDelayed(() -> {
            // Create an ObjectAnimator for fading out the view
            ObjectAnimator fadeOut = ObjectAnimator.ofFloat(overlayView, "alpha", 1f, 0f);
            fadeOut.setDuration(3000);  // Duration of the fade-out animation (3 second)
            fadeOut.start();

            // Remove the overlay after the animation completes
            fadeOut.addListener(new android.animation.AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(android.animation.Animator animation) {
                    removeOverlay();
                }
            });
        }, 2000);
    }
    public static void removeOverlay() {
        if (overlayView != null) {
            windowManager.removeView(overlayView);
            overlayView = null;
        }
    }
}

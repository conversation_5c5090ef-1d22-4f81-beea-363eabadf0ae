import 'package:v18ui/payments/payment_models.dart';

/// Service to manage subscription plans
class SubscriptionPlansService {
  static final SubscriptionPlansService _instance =
      SubscriptionPlansService._internal();

  factory SubscriptionPlansService() => _instance;

  SubscriptionPlansService._internal();

  /// Get available subscription plans
  List<SubscriptionPlan> getAvailablePlans() {
    // Only monthly plan available
    return [
      SubscriptionPlan(
        id: 'monthly',
        name: 'Monthly Subscription',
        description: 'Access all premium features for one month',
        price: 10.0, // TODO: Replace with actual price
        durationInDays: 30,
        features: [
          'All premium features',
          'Porn Deaddiction Mode',
          'Block Adult Sites',
          'Control Gaming Time',
          'Control Entertainment Time',
          'Block Apps',
          'Custom Blocking',
          'Priority Support',
          'Cancel anytime',
        ],
        isPopular: true, // Only plan, so it's the popular choice
      ),
    ];
  }

  /// Get plan by ID
  SubscriptionPlan? getPlanById(String planId) {
    try {
      return getAvailablePlans().firstWhere((plan) => plan.id == planId);
    } catch (e) {
      return null;
    }
  }

  /// Get most popular plan (returns the only available plan)
  SubscriptionPlan? getMostPopularPlan() {
    final plans = getAvailablePlans();
    return plans.isNotEmpty ? plans.first : null;
  }

  /// Calculate savings compared to monthly plan (not applicable for single plan)
  double calculateSavings(SubscriptionPlan plan) {
    // No savings calculation needed for single monthly plan
    return 0.0;
  }

  /// Calculate savings percentage (not applicable for single plan)
  double calculateSavingsPercentage(SubscriptionPlan plan) {
    // No savings calculation needed for single monthly plan
    return 0.0;
  }

  /// Get formatted savings text (not applicable for single plan)
  String getSavingsText(SubscriptionPlan plan) {
    // No savings text needed for single monthly plan
    return '';
  }

  /// Validate plan selection
  bool isValidPlan(String planId) {
    return getPlanById(planId) != null;
  }

  /// Get plan features as formatted string
  String getFormattedFeatures(SubscriptionPlan plan) {
    return plan.features.join('\n• ');
  }

  /// Get plan comparison data
  Map<String, dynamic> getPlanComparison() {
    final plans = getAvailablePlans();
    final monthlyPlan = getPlanById('monthly');

    if (monthlyPlan == null) return {};

    return {
      'plans': plans
          .map((plan) => {
                'plan': plan,
                'savings': calculateSavings(plan),
                'savingsPercentage': calculateSavingsPercentage(plan),
                'monthlyEquivalent': plan.price / (plan.durationInDays / 30),
              })
          .toList(),
    };
  }
}

package com.kespra.v18;

import android.content.ComponentName;
import android.content.Context;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.kespra.v18.Utilities.AdminReceiver;
import com.kespra.v18.Utilities.SettingsBlockingUtils;
import com.kespra.v18.Utilities.WebBlockingUtils;

import java.util.ArrayList;
import java.util.List;

import io.flutter.embedding.android.FlutterFragmentActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugins.GeneratedPluginRegistrant;


public class MainActivity extends FlutterFragmentActivity {

    private static final String CHANNEL = "com.kespra.v18/method_channel";

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public void configureFlutterEngine(@NonNull FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);

        GeneratedPluginRegistrant.registerWith(flutterEngine);

        Context context = getApplicationContext();
        /*Channel for getting Installed Apps list */
        new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), CHANNEL)
                .setMethodCallHandler(
                        (call, result) -> {
                            if (call.method.equals("admin_settings")) {
                                AdminReceiver.flutterResult = result;
                                AdminReceiver adr = new AdminReceiver();
                                ComponentName componentName = new ComponentName(this,AdminReceiver.class);
                                /* showAdminDialog is internally */
                                adr.showAdminDialog(this,componentName);
                            }
                            if (call.method.equals("suspend_package")) {
                                SettingsBlockingUtils blockutil = new SettingsBlockingUtils();
                                ArrayList<String> pkg_nm = call.argument("package_nm");
                                blockutil.suspend_package(context,pkg_nm);
                            }
                            if (call.method.equals("enable_safe_browsing")) {
                                WebBlockingUtils web_blocking = WebBlockingUtils.startWebMonitoring();
                                List<String> installed_browsers = web_blocking.getInstalledBrowsers(context.getPackageManager());
                                System.out.println(installed_browsers);
                            }
                        });
    }
}
